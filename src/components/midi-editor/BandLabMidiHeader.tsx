import React from 'react';
import { MidiEditorState } from './BandLabMidiEditor';
import { getAvailableKeys, getExtendedScaleNotes } from '../../utils/musicTheory';

interface BandLabMidiHeaderProps {
  editorState: MidiEditorState;
  updateEditorState: (updates: Partial<MidiEditorState>) => void;
  onSetReferenceTime: () => void;
  selectedNotes: Set<string | number>;
  onTransposeNotes: (semitones: number) => void;
  onApplyLegato: () => void;
  onQuantizeNotes: () => void;
}

const BandLabMidiHeader: React.FC<BandLabMidiHeaderProps> = ({
  editorState,
  updateEditorState,
  onSetReferenceTime,
  selectedNotes,
  onTransposeNotes,
  onApplyLegato,
  onQuantizeNotes
}) => {
  const handleModeChange = (mode: 'select' | 'addNote' | 'velocity') => {
    updateEditorState({ mode });
  };

  const handleRecordingToggle = () => {
    updateEditorState({ isRecording: !editorState.isRecording });
  };

  const handleKeyChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newKey = e.target.value;
    updateEditorState({ selectedKey: newKey });
    console.log(`🎼 Key signature changed to: ${newKey}`);
  };

  const handleModeTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newMode = e.target.value as 'major' | 'minor';
    updateEditorState({ selectedMode: newMode });
    console.log(`🎼 Mode changed to: ${newMode}`);
  };

  const handleOctaveChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const octave = parseInt(e.target.value) || 4;
    const clampedOctave = Math.max(0, Math.min(8, octave));
    updateEditorState({ selectedOctave: clampedOctave });
    console.log(`🎼 Octave changed to: ${clampedOctave}`);
  };

  const handleBpmChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;

    // Allow empty string temporarily for user to clear the field
    if (value === '') {
      updateEditorState({ bpm: '' as any }); // Temporarily allow empty string
      return;
    }

    const bpm = parseInt(value, 10);
    // Only update if it's a valid positive number
    if (!isNaN(bpm) && bpm > 0) {
      updateEditorState({ bpm });
    }
  };

  const handleBpmBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    const value = e.target.value;

    // If field is empty or invalid when user leaves it, restore default BPM
    if (value === '' || isNaN(parseInt(value, 10)) || parseInt(value, 10) <= 0) {
      updateEditorState({ bpm: 120 });
    }
  };

  const handleQuantizeChange = (quantizeValue: string) => {
    updateEditorState({ quantizeValue });
  };

  const handleTranspose = (semitones: number) => {
    if (selectedNotes.size === 0) {
      console.log('No notes selected for transpose');
      return;
    }
    onTransposeNotes(semitones);
  };

  const handleLegato = () => {
    if (selectedNotes.size < 2) {
      console.log('Please select at least 2 notes to apply legato');
      return;
    }
    onApplyLegato();
  };

  const handleQuantize = () => {
    if (selectedNotes.size === 0) {
      console.log('No notes selected for quantization');
      return;
    }
    onQuantizeNotes();
  };

  return (
    <div className="midi-editor-header">
      {/* First Row - Mode Controls and Note Preview */}
      <div className="midi-editor-header-row">
        <div className="midi-editor-header-section">
          <div className="segmented-control">
            <button
              aria-pressed={editorState.mode === 'select'}
              onClick={() => handleModeChange('select')}
              title="Select Mode"
            >
              <svg className="midi-editor-icon" viewBox="0 0 24 24">
                <path d="M1.39 2.04 9.47 21.8l3.39-6.96 6.54 6.6 1.3-1.3-6.52-6.59 6.8-3.25zM9.6 17.3 4.78 5.48l11.7 4.93-4.3 2.05a1 1 0 0 0-.42.43z" />
              </svg>
            </button>
            <button
              aria-pressed={editorState.mode === 'addNote'}
              onClick={() => handleModeChange('addNote')}
              title="Note Insert Mode (⌘ + Hold)"
            >
              <svg className="midi-editor-icon" viewBox="0 0 24 24">
                <path d="M21.5 8.06a2 2 0 0 0 0-2.82l-2.59-2.59a2 2 0 0 0-2.82 0L3.54 15.19l-.67 6.1 6.09-.69zm-4.41 1.59-9.05 9.04-2.9.33.32-2.91 9.04-9.05zm1.41-1.41-2.59-2.59 1.59-1.59 2.59 2.59z" />
              </svg>
            </button>
            <button
              aria-pressed={editorState.mode === 'velocity'}
              onClick={() => handleModeChange('velocity')}
              title="Toggle Velocity Mode (V)"
            >
              <svg className="midi-editor-icon" viewBox="0 0 24 24">
                <path d="m4.14 3 6.95 16.59h1.84L19.87 3h-2.16L12 16.61 6.3 3z" />
              </svg>
            </button>
            <button
              aria-pressed={editorState.isRecording}
              onClick={handleRecordingToggle}
              title="Toggle Recording (Shift+R)"
              className={editorState.isRecording ? 'recording' : ''}
            >
              <svg className="midi-editor-icon" viewBox="0 0 24 24">
                <circle cx="12" cy="12" r="10" fill={editorState.isRecording ? '#ff4444' : 'currentColor'} />
                {editorState.isRecording && <circle cx="12" cy="12" r="4" fill="white" />}
              </svg>
            </button>
          </div>

          <button
            className={`midi-editor-button icon-only ${editorState.notePreview ? 'active' : ''}`}
            onClick={() => updateEditorState({ notePreview: !editorState.notePreview })}
            title="Note Preview"
          >
            <svg className="midi-editor-icon" viewBox="0 0 24 24">
              <path d="M12.06 3C7.24 3 4 6.93 4 11.8v1.37A3 3 0 0 1 5 13h3a1 1 0 0 1 1 1v7a1 1 0 0 1-1 1H6a4 4 0 0 1-4-4v-6.2C2 6.06 5.91 1 12.06 1 18.23 1 22 6.08 22 11.8V18a4 4 0 0 1-4 4h-2a1 1 0 0 1-1-1v-7a1 1 0 0 1 1-1h3a3 3 0 0 1 1 .17V11.8C20 6.9 16.87 3 12.06 3M20 16a1 1 0 0 0-1-1h-2v5h1a2 2 0 0 0 2-2zM6 20a2 2 0 0 1-2-2v-2.02A1 1 0 0 1 5 15h2v5z" />
            </svg>
          </button>

          <button
            className={`midi-editor-button icon-only ${editorState.metronomeEnabled ? 'active' : ''}`}
            onClick={() => updateEditorState({ metronomeEnabled: !editorState.metronomeEnabled })}
            title="Toggle Metronome"
          >
            <svg className="midi-editor-icon" viewBox="0 0 24 24">
              <path d="M12 2L8 6v14c0 1.1.9 2 2 2h4c1.1 0 2-.9 2-2V6l-4-4zm0 2.83L14.17 7H9.83L12 4.83zM10 9h4v11h-4V9zm2 1v8l2-4-2-4z"/>
            </svg>
          </button>
        </div>

        <div className="midi-editor-header-section">
          <div className="midi-editor-header-title">
            <svg className="midi-editor-icon small" viewBox="0 0 24 24">
              <path d="M21 1.8V17a4 4 0 1 1-2-3.46V4.2l-9 1.63V19a4 4 0 1 1-2-3.46V4.17zM19 17a2 2 0 1 0-4 0 2 2 0 0 0 4 0M6 17a2 2 0 1 1 0 4 2 2 0 0 1 0-4" />
            </svg>
            MIDI Notes
          </div>
        </div>

        <div className="midi-editor-header-section">
          <span>Key:</span>
          <select
            className="midi-editor-dropdown"
            value={editorState.selectedKey}
            onChange={handleKeyChange}
            title="Select root note for scale degree playing"
          >
            {getAvailableKeys().map(key => (
              <option key={key} value={key}>{key}</option>
            ))}
          </select>
          <select
            className="midi-editor-dropdown"
            value={editorState.selectedMode}
            onChange={handleModeTypeChange}
            title="Select major or minor mode"
          >
            <option value="major">Major</option>
            <option value="minor">Minor</option>
          </select>
        </div>

        <div className="midi-editor-header-section">
          <span>Octave:</span>
          <input
            type="number"
            className="midi-editor-input"
            value={editorState.selectedOctave}
            onChange={handleOctaveChange}
            min="0"
            max="8"
            title="Octave for keyboard note playing (keys 1-7)"
          />
        </div>

        <div className="midi-editor-header-section">
          <span>BPM:</span>
          <input
            type="number"
            className="midi-editor-input"
            value={editorState.bpm === '' ? '' : editorState.bpm}
            onChange={handleBpmChange}
            onBlur={handleBpmBlur}
            min="1"
            max="300"
            placeholder="120"
          />
          <button
            className="midi-editor-button compact"
            onClick={onSetReferenceTime}
            title="Set Reference Time (R)"
          >
            Set Ref Time
          </button>
          {editorState.referenceStartTime !== null && (
            <span className="text-secondary">
              Ref: {editorState.referenceStartTime.toFixed(2)}s
            </span>
          )}
        </div>

        <div className="midi-editor-header-section">
          <span>Zoom:</span>
          <button
            className="midi-editor-button compact"
            onClick={() => updateEditorState({ zoom: Math.max(0.25, editorState.zoom - 0.25) })}
            disabled={editorState.zoom <= 0.25}
          >
            -
          </button>
          <span className="tabular-nums" style={{ minWidth: '40px', textAlign: 'center' }}>
            {Math.round(editorState.zoom * 100)}%
          </span>
          <button
            className="midi-editor-button compact"
            onClick={() => updateEditorState({ zoom: Math.min(4, editorState.zoom + 0.25) })}
            disabled={editorState.zoom >= 4}
          >
            +
          </button>
        </div>

        <div className="midi-editor-header-section">
          <span>Note Height:</span>
          <button
            className="midi-editor-button compact"
            onClick={() => updateEditorState({ noteHeight: Math.max(10, (editorState.noteHeight || 20) - 2) })}
            disabled={(editorState.noteHeight || 20) <= 10}
            title="Decrease note height"
          >
            -
          </button>
          <span className="tabular-nums" style={{ minWidth: '30px', textAlign: 'center' }}>
            {editorState.noteHeight || 20}px
          </span>
          <button
            className="midi-editor-button compact"
            onClick={() => updateEditorState({ noteHeight: Math.min(40, (editorState.noteHeight || 20) + 2) })}
            disabled={(editorState.noteHeight || 20) >= 40}
            title="Increase note height"
          >
            +
          </button>
        </div>

        <div className="midi-editor-header-section">
          <button
            className={`midi-editor-button compact ${editorState.stickyPlayhead ? 'active' : ''}`}
            onClick={() => updateEditorState({ stickyPlayhead: !editorState.stickyPlayhead })}
            title="Toggle sticky playhead (follows playback)"
          >
            {editorState.stickyPlayhead ? 'Sticky ON' : 'Sticky OFF'}
          </button>
        </div>


      </div>

      {/* Second Row - Note Controls */}
      <div className="midi-editor-header-row">
        <div className="midi-editor-header-section">
          <span>Velocity</span>
          <span className="tabular-nums">{editorState.velocity}</span>
        </div>

        <div className="midi-editor-header-section">
          <span>Scale Notes (2 Octaves):</span>
          <div style={{ display: 'flex', flexWrap: 'wrap', gap: '3px', fontSize: '10px', color: '#999', maxWidth: '600px' }}>
            {getExtendedScaleNotes(editorState.selectedKey, editorState.selectedMode, editorState.selectedOctave, 14).map((note, index) => (
              <span
                key={index}
                title={note.displayName}
                style={{
                  backgroundColor: index < 7 ? '#f0f0f0' : '#e0e0e0',
                  padding: '1px 3px',
                  borderRadius: '2px',
                  border: '1px solid #ccc'
                }}
              >
                {note.keyBinding}:{note.noteName}{note.noteOctave}
              </span>
            ))}
          </div>
        </div>

        <div className="midi-editor-header-section">
          <div className="row-gap-4">
            <button
              className="midi-editor-button compact"
              onClick={handleLegato}
              disabled={selectedNotes.size < 2}
              title={selectedNotes.size < 2 ? 'Select at least 2 notes to apply legato' : `Apply legato to ${selectedNotes.size} selected notes`}
            >
              Legato
            </button>
          </div>
        </div>

        <div className="midi-editor-header-section">
          <div className="row-gap-4">
            <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '2px' }}>
                <select
                  className="midi-editor-dropdown"
                  value={editorState.quantizeValue}
                  onChange={(e) => handleQuantizeChange(e.target.value)}
                  title="Grid subdivision for note placement and snapping"
                >
                  <option value="free">Free</option>
                  <option value="1/32">1/32</option>
                  <option value="1/16">1/16</option>
                  <option value="1/8">1/8</option>
                  <option value="1/4">1/4</option>
                  <option value="1/2">1/2</option>
                  <option value="1">1</option>
                </select>
              </div>
              <button
                className="midi-editor-button compact"
                onClick={handleQuantize}
                disabled={selectedNotes.size === 0 || editorState.freeMovement || editorState.quantizeValue === 'free'}
                title={
                  selectedNotes.size === 0
                    ? 'Select notes to quantize'
                    : editorState.freeMovement || editorState.quantizeValue === 'free'
                    ? 'Cannot quantize in free mode'
                    : `Quantize ${selectedNotes.size} selected notes to ${editorState.quantizeValue} grid`
                }
              >
                Quantize
              </button>
              <button
                className={`midi-editor-button compact ${editorState.freeMovement || editorState.quantizeValue === 'free' ? 'active' : ''}`}
                onClick={() => {
                  if (editorState.quantizeValue === 'free' || editorState.freeMovement) {
                    // Switch to grid mode
                    updateEditorState({
                      freeMovement: false,
                      quantizeValue: editorState.quantizeValue === 'free' ? '1/16' : editorState.quantizeValue
                    });
                  } else {
                    // Switch to free mode
                    updateEditorState({ freeMovement: true });
                  }
                }}
                title={`Toggle Free Movement (F) - Currently: ${editorState.freeMovement || editorState.quantizeValue === 'free' ? 'Free' : 'Grid'}`}
              >
                {editorState.freeMovement || editorState.quantizeValue === 'free' ? 'Free' : 'Grid'}
              </button>
            </div>
          </div>
        </div>

        <div className="midi-editor-header-section">
          <span>Transpose {selectedNotes.size > 0 && `(${selectedNotes.size} notes)`}</span>
          <div style={{ display: 'flex', gap: '4px' }}>
            <button
              className="midi-editor-button compact"
              onClick={() => handleTranspose(1)}
              disabled={selectedNotes.size === 0}
              title={selectedNotes.size === 0 ? 'Select notes to transpose' : 'Transpose up 1 semitone'}
            >
              +1
            </button>
            <button
              className="midi-editor-button compact"
              onClick={() => handleTranspose(-1)}
              disabled={selectedNotes.size === 0}
              title={selectedNotes.size === 0 ? 'Select notes to transpose' : 'Transpose down 1 semitone'}
            >
              -1
            </button>
            <button
              className="midi-editor-button compact"
              onClick={() => handleTranspose(12)}
              disabled={selectedNotes.size === 0}
              title={selectedNotes.size === 0 ? 'Select notes to transpose' : 'Transpose up 1 octave'}
            >
              +12
            </button>
            <button
              className="midi-editor-button compact"
              onClick={() => handleTranspose(-12)}
              disabled={selectedNotes.size === 0}
              title={selectedNotes.size === 0 ? 'Select notes to transpose' : 'Transpose down 1 octave'}
            >
              -12
            </button>
          </div>
        </div>
      </div>


    </div>
  );
};

export default BandLabMidiHeader;
