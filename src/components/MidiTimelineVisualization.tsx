import React, { useState, useRef } from 'react';
import { MidiNote } from '../types/midi';
import { useAudioStore } from '../store/useAudioStore';
import { noteNameToSemitone, SCALE_PATTERNS, ScaleMode } from '../utils/musicTheory';

interface MidiTimelineVisualizationProps {
  notes: MidiNote[];
  bpm?: number; // Beats per minute to convert note timing to video time
  zoomLevel?: number; // Zoom level from the main timeline
  height?: number; // Height of the visualization
  referenceStartTime?: number | null; // Reference start time from MIDI editor
  selectedKey?: string; // Key signature for scale degree coloring
  selectedMode?: ScaleMode; // Major or minor mode
}

const MidiTimelineVisualization: React.FC<MidiTimelineVisualizationProps> = ({
  notes,
  bpm = 120, // Default BPM
  zoomLevel = 1,
  height = 100,
  referenceStartTime = null,
  selectedKey = 'C',
  selectedMode = 'major'
}) => {
  const { currentTime } = useAudioStore();
  
  // Local zoom states for the MIDI visualization
  const [verticalZoom, setVerticalZoom] = useState(1);
  const [horizontalZoom, setHorizontalZoom] = useState(1);
  const [isExpanded, setIsExpanded] = useState(false);
  const [noteHeight, setNoteHeight] = useState(12); // Default note height in pixels
  const [stickyPlayhead, setStickyPlayhead] = useState(true); // Playhead follows playback
  const containerRef = useRef<HTMLDivElement>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  
  // Calculate effective height based on expansion and zoom
  const effectiveHeight = isExpanded ? Math.max(height * 2, 200) : height;
  const zoomedHeight = effectiveHeight * verticalZoom;
  
  // Piano keyboard configuration
  const keyboardWidth = 160; // Width of the piano keyboard on the left

  // Use the same pixel-based coordinate system as the MIDI editor for perfect alignment
  const pixelsPerBeat = 40; // Match BandLabNoteGrid base value (before zoom)
  const beatsPerMeasure = 4;
  const measuresVisible = 32; // Match BandLabNoteGrid
  const totalBeats = measuresVisible * beatsPerMeasure;

  // Convert beats to pixel position (same as MIDI editor)
  const beatsToPixels = (beats: number): number => {
    return beats * pixelsPerBeat * zoomLevel * horizontalZoom;
  };

  // Convert video time to beats, then to pixels
  const timeToPixels = (timeInSeconds: number): number => {
    if (referenceStartTime === null) {
      return 0;
    }
    const elapsedTime = timeInSeconds - referenceStartTime;
    if (elapsedTime < 0) return 0;

    const beatsPerSecond = bpm / 60;
    const elapsedBeats = elapsedTime * beatsPerSecond;
    return beatsToPixels(elapsedBeats);
  };

  // Convert MIDI pitch to vertical position (higher pitch = higher on screen = lower y value)
  const pitchToY = (pitch: number): number => {
    // MIDI pitch range typically 21 (A0) to 108 (C8)
    // We'll use a smaller range for better visualization: 36 (C2) to 84 (C6)
    const minPitch = 36;
    const maxPitch = 84;
    const visualHeight = zoomedHeight - 40; // Leave space for labels and padding

    // Clamp pitch to our range
    const clampedPitch = Math.max(minPitch, Math.min(maxPitch, pitch));

    // Convert to y position (higher pitch = lower y value, so higher on screen)
    const normalizedPitch = (clampedPitch - minPitch) / (maxPitch - minPitch);
    return 20 + ((1 - normalizedPitch) * visualHeight); // 20px top padding for controls, inverted so high pitch = low y
  };

  // Get scale degree for a given MIDI pitch
  const getScaleDegree = (pitch: number): number | null => {
    const rootSemitone = noteNameToSemitone(selectedKey);
    const pattern = SCALE_PATTERNS[selectedMode];
    const noteIndex = pitch % 12;
    
    // Find which scale degree this note corresponds to
    for (let i = 0; i < pattern.length; i++) {
      const scaleSemitone = (rootSemitone + pattern[i]) % 12;
      if (scaleSemitone === noteIndex) {
        return i + 1; // Return 1-based scale degree
      }
    }
    return null; // Not in scale
  };

  // Get note color based on scale degree
  const getNoteColor = (pitch: number): string => {
    const scaleDegree = getScaleDegree(pitch);
    
    if (scaleDegree === null) {
      // Not in scale - use gray
      return '#9ca3af';
    }
    
    // Scale degree colors (1=red, 2=orange, 3=yellow, 4=green, 5=dark blue, 6=purple, 7=magenta)
    const scaleColors = [
      '#ef4444', // 1 (Root) - red
      '#f97316', // 2 - orange  
      '#eab308', // 3 - yellow
      '#22c55e', // 4 - green
      '#1e40af', // 5 - dark blue
      '#8b5cf6', // 6 - purple
      '#ec4899', // 7 - magenta
    ];
    
    return scaleColors[scaleDegree - 1] || '#9ca3af';
  };

  // Get note name for tooltip
  const getNoteNameFromPitch = (pitch: number): string => {
    const noteNames = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B'];
    const octave = Math.floor(pitch / 12) - 1;
    const noteIndex = pitch % 12;
    return `${noteNames[noteIndex]}${octave}`;
  };

  // Check if a note is a black key
  const isBlackKey = (pitch: number): boolean => {
    const noteIndex = pitch % 12;
    return [1, 3, 6, 8, 10].includes(noteIndex); // C#, D#, F#, G#, A#
  };

  // Get the height for each semitone based on zoom
  const getSemitoneHeight = (): number => {
    const minPitch = 36; // C2
    const maxPitch = 84; // C6
    const totalSemitones = maxPitch - minPitch + 1;
    const visualHeight = zoomedHeight - 40;
    return visualHeight / totalSemitones;
  };

  // Generate piano keys for the visible range
  const generatePianoKeys = () => {
    const keys = [];
    const minPitch = 36; // C2
    const maxPitch = 84; // C6
    const semitoneHeight = getSemitoneHeight();

    // First, render all white keys
    for (let pitch = minPitch; pitch <= maxPitch; pitch++) {
      const isBlack = isBlackKey(pitch);
      if (!isBlack) {
        const scaleDegree = getScaleDegree(pitch);
        const isInScale = scaleDegree !== null;

        keys.push(
          <div
            key={`white-${pitch}`}
            style={{
              position: 'absolute',
              left: '0px',
              top: `${pitchToY(pitch) - semitoneHeight/2}px`,
              width: '100px',
              height: `${semitoneHeight}px`,
              backgroundColor: isInScale ? getNoteColor(pitch) : '#f9fafb',
              border: '1px solid #d1d5db',
              borderRadius: '0px',
              opacity: isInScale ? 0.9 : 1,
              zIndex: 1,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
            title={`${getNoteNameFromPitch(pitch)}${scaleDegree ? ` - Scale degree ${scaleDegree}` : ' - Not in scale'}`}
          >
            <span style={{
              fontSize: '10px',
              fontWeight: 'bold',
              color: isInScale ? 'white' : '#374151',
              textShadow: isInScale ? '0 1px 1px rgba(0, 0, 0, 0.5)' : 'none'
            }}>
              {getNoteNameFromPitch(pitch).replace(/\d+$/, '')}
            </span>
          </div>
        );
      }
    }

    // Then, render all black keys on top
    for (let pitch = minPitch; pitch <= maxPitch; pitch++) {
      const isBlack = isBlackKey(pitch);
      if (isBlack) {
        const scaleDegree = getScaleDegree(pitch);
        const isInScale = scaleDegree !== null;

        keys.push(
          <div
            key={`black-${pitch}`}
            style={{
              position: 'absolute',
              left: '60px',
              top: `${pitchToY(pitch) - semitoneHeight/2}px`,
              width: '40px',
              height: `${semitoneHeight}px`,
              backgroundColor: isInScale ? getNoteColor(pitch) : '#374151',
              border: '1px solid #1f2937',
              borderRadius: '0px',
              opacity: isInScale ? 0.9 : 1,
              zIndex: 2,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
            title={`${getNoteNameFromPitch(pitch)}${scaleDegree ? ` - Scale degree ${scaleDegree}` : ' - Not in scale'}`}
          >
            {isInScale && (
              <span style={{
                fontSize: '9px',
                fontWeight: 'bold',
                color: 'white',
                textShadow: '0 1px 1px rgba(0, 0, 0, 0.5)'
              }}>
                {scaleDegree}
              </span>
            )}
          </div>
        );
      }
    }

    return keys;
  };

  // Calculate current playhead position using pixel-based system
  const playheadPixels = timeToPixels(currentTime);
  const totalWidthPixels = totalBeats * pixelsPerBeat * zoomLevel * horizontalZoom;

  // Auto-scroll to keep playhead visible when sticky playhead is enabled
  React.useEffect(() => {
    if (!stickyPlayhead || !scrollContainerRef.current || referenceStartTime === null) return;

    const scrollContainer = scrollContainerRef.current;
    const containerWidth = scrollContainer.clientWidth;
    const scrollLeft = scrollContainer.scrollLeft;

    // Calculate if playhead is outside the visible area
    const playheadPosition = playheadPixels;
    const leftEdge = scrollLeft;
    const rightEdge = scrollLeft + containerWidth;

    // Keep playhead in the center third of the visible area
    const centerThird = containerWidth / 3;
    const targetScrollLeft = playheadPosition - centerThird;

    // Only scroll if playhead is getting close to edges or outside visible area
    if (playheadPosition < leftEdge + centerThird || playheadPosition > rightEdge - centerThird) {
      scrollContainer.scrollTo({
        left: Math.max(0, targetScrollLeft),
        behavior: 'smooth'
      });
    }
  }, [playheadPixels, stickyPlayhead, referenceStartTime]);

  return (
    <div 
      ref={containerRef}
      style={{
        width: '100%',
        backgroundColor: '#f8fafc',
        border: '1px solid #e2e8f0',
        borderRadius: '0.375rem',
        position: 'relative',
        marginTop: '0.5rem'
      }}
    >
      {/* Controls */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: '4px 8px',
        backgroundColor: '#e2e8f0',
        borderBottom: '1px solid #cbd5e1',
        fontSize: '10px'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <span style={{ fontWeight: 'bold', color: '#374151' }}>
            MIDI Notes: {selectedKey} {selectedMode}
          </span>
          {referenceStartTime !== null && (
            <span style={{ color: '#6b7280' }}>
              Ref: {referenceStartTime.toFixed(2)}s
            </span>
          )}
        </div>
        <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
          <button
            onClick={() => setHorizontalZoom(prev => Math.max(0.5, prev - 0.25))}
            style={{
              padding: '2px 6px',
              fontSize: '10px',
              backgroundColor: '#f3f4f6',
              border: '1px solid #d1d5db',
              borderRadius: '2px',
              cursor: 'pointer'
            }}
            disabled={horizontalZoom <= 0.5}
          >
            H-
          </button>
          <span style={{ fontSize: '9px', color: '#6b7280', minWidth: '30px', textAlign: 'center' }}>
            {horizontalZoom.toFixed(1)}x
          </span>
          <button
            onClick={() => setHorizontalZoom(prev => Math.min(4, prev + 0.25))}
            style={{
              padding: '2px 6px',
              fontSize: '10px',
              backgroundColor: '#f3f4f6',
              border: '1px solid #d1d5db',
              borderRadius: '2px',
              cursor: 'pointer'
            }}
            disabled={horizontalZoom >= 4}
          >
            H+
          </button>
          <button
            onClick={() => setVerticalZoom(prev => Math.max(0.5, prev - 0.25))}
            style={{
              padding: '2px 6px',
              fontSize: '10px',
              backgroundColor: '#f3f4f6',
              border: '1px solid #d1d5db',
              borderRadius: '2px',
              cursor: 'pointer'
            }}
            disabled={verticalZoom <= 0.5}
          >
            V-
          </button>
          <span style={{ fontSize: '9px', color: '#6b7280', minWidth: '30px', textAlign: 'center' }}>
            {verticalZoom.toFixed(1)}x
          </span>
          <button
            onClick={() => setVerticalZoom(prev => Math.min(3, prev + 0.25))}
            style={{
              padding: '2px 6px',
              fontSize: '10px',
              backgroundColor: '#f3f4f6',
              border: '1px solid #d1d5db',
              borderRadius: '2px',
              cursor: 'pointer'
            }}
            disabled={verticalZoom >= 3}
          >
            V+
          </button>
          <button
            onClick={() => setNoteHeight(prev => Math.max(6, prev - 2))}
            style={{
              padding: '2px 6px',
              fontSize: '10px',
              backgroundColor: '#f3f4f6',
              border: '1px solid #d1d5db',
              borderRadius: '2px',
              cursor: 'pointer'
            }}
            disabled={noteHeight <= 6}
            title="Decrease note height"
          >
            N-
          </button>
          <span style={{ fontSize: '9px', color: '#6b7280', minWidth: '25px', textAlign: 'center' }}>
            {noteHeight}px
          </span>
          <button
            onClick={() => setNoteHeight(prev => Math.min(24, prev + 2))}
            style={{
              padding: '2px 6px',
              fontSize: '10px',
              backgroundColor: '#f3f4f6',
              border: '1px solid #d1d5db',
              borderRadius: '2px',
              cursor: 'pointer'
            }}
            disabled={noteHeight >= 24}
            title="Increase note height"
          >
            N+
          </button>
          <button
            onClick={() => setStickyPlayhead(!stickyPlayhead)}
            style={{
              padding: '2px 8px',
              fontSize: '10px',
              backgroundColor: stickyPlayhead ? '#22c55e' : '#f3f4f6',
              color: stickyPlayhead ? 'white' : '#374151',
              border: '1px solid #d1d5db',
              borderRadius: '2px',
              cursor: 'pointer',
              marginLeft: '4px'
            }}
            title="Toggle sticky playhead (follows playback)"
          >
            {stickyPlayhead ? 'Sticky ON' : 'Sticky OFF'}
          </button>
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            style={{
              padding: '2px 8px',
              fontSize: '10px',
              backgroundColor: isExpanded ? '#3b82f6' : '#f3f4f6',
              color: isExpanded ? 'white' : '#374151',
              border: '1px solid #d1d5db',
              borderRadius: '2px',
              cursor: 'pointer',
              marginLeft: '4px'
            }}
          >
            {isExpanded ? 'Collapse' : 'Expand'}
          </button>
        </div>
      </div>

      {/* Main Content Area */}
      <div style={{ display: 'flex', height: `${zoomedHeight}px` }}>
        {/* Piano Keyboard */}
        <div style={{
          width: `${keyboardWidth}px`,
          backgroundColor: '#f9fafb',
          borderRight: '1px solid #e2e8f0',
          position: 'relative',
          flexShrink: 0
        }}>
          {generatePianoKeys()}
        </div>

        {/* Timeline Area */}
        <div
          ref={scrollContainerRef}
          style={{
            flex: 1,
            position: 'relative',
            overflow: 'auto'
          }}
        >
          {/* Background grid lines for pitch reference */}
          <div style={{ position: 'absolute', width: '100%', height: '100%' }}>
            {[84, 72, 60, 48, 36].map(pitch => (
              <div
                key={pitch}
                style={{
                  position: 'absolute',
                  left: 0,
                  right: 0,
                  top: `${pitchToY(pitch)}px`,
                  height: '1px',
                  backgroundColor: '#e2e8f0',
                  opacity: 0.5
                }}
              />
            ))}
          </div>

          {/* MIDI Notes */}
          <div style={{
            position: 'relative',
            width: `${totalWidthPixels}px`,
            height: '100%',
            minWidth: '100%'
          }}>
            {notes.map(note => {
              const startPixels = beatsToPixels(note.startTime);
              const widthPixels = beatsToPixels(note.duration);

              // Only render notes that are visible in the current viewport
              if (startPixels > totalWidthPixels || startPixels + widthPixels < 0) {
                return null;
              }

              // Calculate if we should show note name based on actual pixel width
              const showNoteName = widthPixels > 40; // Show note name if wider than 40px
              const scaleDegree = getScaleDegree(note.pitch);

              return (
                <div
                  key={note.id}
                  style={{
                    position: 'absolute',
                    left: `${startPixels}px`,
                    top: `${pitchToY(note.pitch) - noteHeight/2}px`,
                    width: `${Math.max(widthPixels, 8)}px`, // Minimum width for visibility
                    height: `${noteHeight}px`,
                    backgroundColor: getNoteColor(note.pitch),
                    borderRadius: '4px',
                    opacity: 0.9,
                    zIndex: 2,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'flex-start',
                    paddingLeft: showNoteName ? '6px' : '0',
                    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.15)',
                    border: '1px solid rgba(255, 255, 255, 0.3)'
                  }}
                  title={`${getNoteNameFromPitch(note.pitch)} - Beat ${note.startTime.toFixed(2)} - ${note.duration.toFixed(2)} beats${scaleDegree ? ` - Scale degree ${scaleDegree}` : ' - Not in scale'}`}
                >
                  {showNoteName && (
                    <span style={{
                      fontSize: '10px',
                      fontWeight: 'bold',
                      color: 'white',
                      textShadow: '0 1px 1px rgba(0, 0, 0, 0.5)',
                      whiteSpace: 'nowrap'
                    }}>
                      {scaleDegree ? `${scaleDegree}` : getNoteNameFromPitch(note.pitch).replace(/\d+$/, '')}
                    </span>
                  )}
                </div>
              );
            })}
          </div>

          {/* Playhead */}
          {referenceStartTime !== null && (
            <div
              style={{
                position: 'absolute',
                left: `${playheadPixels}px`,
                top: 0,
                bottom: 0,
                width: '2px',
                backgroundColor: '#dc2626',
                zIndex: 10,
                pointerEvents: 'none'
              }}
            />
          )}
        </div>
      </div>

      {/* Scale Degree Legend */}
      <div style={{
        position: 'absolute',
        bottom: '4px',
        left: `${keyboardWidth + 8}px`,
        fontSize: '9px',
        color: '#64748b',
        backgroundColor: 'rgba(248, 250, 252, 0.9)',
        padding: '3px 6px',
        borderRadius: '3px',
        border: '1px solid #e2e8f0',
        display: 'flex',
        gap: '6px',
        alignItems: 'center'
      }}>
        <span>Scale degrees:</span>
        {[1, 2, 3, 4, 5, 6, 7].map(degree => {
          const colors = ['#ef4444', '#f97316', '#eab308', '#22c55e', '#1e40af', '#8b5cf6', '#ec4899'];
          return (
            <div key={degree} style={{ display: 'flex', alignItems: 'center', gap: '2px' }}>
              <div style={{
                width: '8px',
                height: '8px',
                backgroundColor: colors[degree - 1],
                borderRadius: '2px',
                border: '1px solid rgba(255, 255, 255, 0.3)'
              }} />
              <span>{degree}</span>
            </div>
          );
        })}
      </div>

      {/* Info Labels */}
      <div style={{
        position: 'absolute',
        bottom: '4px',
        right: '8px',
        fontSize: '10px',
        color: '#64748b',
        backgroundColor: 'rgba(248, 250, 252, 0.9)',
        padding: '3px 6px',
        borderRadius: '3px',
        border: '1px solid #e2e8f0'
      }}>
        Notes: {notes.length} | BPM: {bpm} | H: {horizontalZoom.toFixed(1)}x | V: {verticalZoom.toFixed(1)}x
      </div>
    </div>
  );
};

export default MidiTimelineVisualization;
